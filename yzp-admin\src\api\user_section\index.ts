import { http } from "@/utils/http";

// 获取用户头像列表
const getUserSection = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userImageAudit/queryList", {
    data
  });
};

// 用户头像审批
const handleImageAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userImageAudit/audit", {
    data
  });
};

// 获取证书列表
const getCertificateList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/certificateAudit/queryList", {
    data
  });
};

// 证书审批
const handleCertificateAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/certificateAudit/audit", {
    data
  });
};

// 获取附件简历列表
const getAppendixList = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userFileResumeAudit/queryList",
    {
      data
    }
  );
};

// 根据ID获取附件简历
const getAppendixById = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userFileResumeAudit/queryById",
    {
      data
    }
  );
};

// 附件简历审核
const handleAppendixAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userFileResumeAudit/audit", {
    data
  });
};

// 获取作品集
const getPortfolioList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/resumeFileAudit/queryList", {
    data
  });
};

// 作品集审核
const handlePortfolioAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/resumeFileAudit/audit", {
    data
  });
};

export {
  getUserSection,
  handleImageAudit,
  getCertificateList,
  handleCertificateAudit,
  getAppendixList,
  getAppendixById,
  handleAppendixAudit,
  getPortfolioList,
  handlePortfolioAudit
};
