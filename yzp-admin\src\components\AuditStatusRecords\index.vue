<script setup lang="ts">
import { formatTimestamp } from "@/utils/dateFormat";
const props = defineProps<{
  auditList: Array<{
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }>;
  statusList: Array<{ person: string; time: string; person2: string }>;
}>();
</script>

<template>
  <div class="audit-status-records-box">
    <div class="scroll-container">
      <div v-if="auditList.length > 0">
        <div class="record-title">审核记录</div>
        <el-table
          :data="auditList"
          border
          class="audit-table"
          :show-header="true"
          :header-cell-style="{
            background: '#f7f8fa',
            color: '#888',
            fontWeight: 500,
            fontSize: '15px',
            textAlign: 'center'
          }"
          :cell-style="{
            background: '#f7f8fa',
            color: '#222',
            fontSize: '15px',
            textAlign: 'center'
          }"
          style="width: 100%; margin-bottom: 24px"
        >
          <el-table-column prop="auditUserName" label="人员" min-width="80" />
          <el-table-column
            prop="auditTime"
            label="审核时间"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ formatTimestamp(scope.row.auditTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="auditList.some(item => item.status === '2')"
            prop="reason"
            label="驳回原因"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="scope.row.status === '2'">{{
                scope.row.reason
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="结果" min-width="80">
            <template #default="scope">
              <span
                :style="{
                  color:
                    scope.row.status === '1'
                      ? '#1FC600'
                      : scope.row.status === '2'
                        ? '#F56C6C'
                        : '#222'
                }"
              >
                {{
                  scope.row.status === "1"
                    ? "通过"
                    : scope.row.status === "2"
                      ? "已驳回"
                      : "待审核"
                }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="statusList.length > 0">
        <div class="record-title">状态记录</div>
        <el-table
          :data="statusList"
          border
          class="status-table"
          :show-header="true"
          :header-cell-style="{
            background: '#f7f8fa',
            color: '#888',
            fontWeight: 500,
            fontSize: '15px',
            textAlign: 'center'
          }"
          :cell-style="{
            background: '#f7f8fa',
            color: '#222',
            fontSize: '15px',
            textAlign: 'center'
          }"
          style="width: 100%"
        >
          <el-table-column prop="person" label="人员" min-width="80" />
          <el-table-column prop="time" label="转审时间" min-width="160" />
          <el-table-column prop="person2" label="人员" min-width="80" />
        </el-table>
      </div>
      <div v-if="statusList.length === 0 && auditList.length === 0">
        <div class="no-data">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audit-status-records-box {
  background: #fff;
  border-radius: 12px;
  padding: 24px 24px 18px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 380px;
  max-height: 350px;
  overflow: hidden; // 防止内容外溢
}

.scroll-container {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.record-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
}

.audit-table,
.status-table {
  background: #f7f8fa;
  border-radius: 8px;
  overflow: hidden;
}

.no-data {
  text-align: center;
  color: #888;
  font-size: 16px;
  padding: 32px 0;
}
</style>
