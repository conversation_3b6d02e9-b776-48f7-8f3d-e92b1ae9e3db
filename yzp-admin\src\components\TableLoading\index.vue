<template>
  <div class="table-loading-container">
    <!-- 自定义 Loading 遮罩 -->
    <div v-if="loading" class="custom-loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
    
    <!-- 表格内容 -->
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean;
  loadingText?: string;
}

withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '数据加载中...'
});
</script>

<style scoped lang="scss">
.table-loading-container {
  position: relative;
  width: 100%;
}

.custom-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  .loading-spinner {
    position: relative;
    width: 60px;
    height: 60px;
    
    .spinner-ring {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: spin 1.2s linear infinite;
      
      &:nth-child(1) {
        animation-delay: 0s;
      }
      
      &:nth-child(2) {
        width: 80%;
        height: 80%;
        top: 10%;
        left: 10%;
        border-top-color: #67c23a;
        animation-delay: -0.4s;
      }
      
      &:nth-child(3) {
        width: 60%;
        height: 60%;
        top: 20%;
        left: 20%;
        border-top-color: #e6a23c;
        animation-delay: -0.8s;
      }
    }
  }
  
  .loading-text {
    margin: 0;
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 暗色主题适配 */
html.dark .custom-loading-overlay {
  background: rgba(0, 0, 0, 0.8);
  
  .loading-text {
    color: #e5eaf3;
  }
}
</style>
