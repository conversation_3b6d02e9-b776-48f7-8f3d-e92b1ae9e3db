/**
 * 日期时间格式化工具函数
 */

/**
 * 格式化时间戳为标准日期时间字符串
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 *
 * @example
 * formatTimestamp(1640995200) // '2022-01-01 00:00:00'
 * formatTimestamp(1640995200000) // '2022-01-01 00:00:00'
 * formatTimestamp('1640995200') // '2022-01-01 00:00:00'
 * formatTimestamp(1640995200, 'YYYY/MM/DD') // '2022/01/01'
 */
export const formatTimestamp = (
  timestamp: number | string,
  format: string = "YYYY-MM-DD HH:mm:ss"
): string => {
  if (!timestamp) return "";

  // 如果是字符串，尝试转换为数字
  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;

  // 检查是否为有效的时间戳（10位或13位）
  if (isNaN(time) || time <= 0) return "";

  // 如果是10位时间戳，转换为13位
  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const date = new Date(milliseconds);

  // 检查日期是否有效
  if (isNaN(date.getTime())) return "";

  // 格式化日期时间
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  // 根据格式模板替换
  return format
    .replace(/YYYY/g, year.toString())
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds);
};

/**
 * 格式化时间戳为日期字符串（不包含时间）
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @param separator 分隔符，默认为 '-'
 * @returns 格式化后的日期字符串
 *
 * @example
 * formatDate(1640995200) // '2022-01-01'
 * formatDate(1640995200, '/') // '2022/01/01'
 */
export const formatDate = (
  timestamp: number | string,
  separator: string = "-"
): string => {
  return formatTimestamp(timestamp, `YYYY${separator}MM${separator}DD`);
};

/**
 * 格式化时间戳为时间字符串（不包含日期）
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @param separator 分隔符，默认为 ':'
 * @returns 格式化后的时间字符串
 *
 * @example
 * formatTime(1640995200) // '00:00:00'
 * formatTime(1640995200, '-') // '00-00-00'
 */
export const formatTime = (
  timestamp: number | string,
  separator: string = ":"
): string => {
  return formatTimestamp(timestamp, `HH${separator}mm${separator}ss`);
};

/**
 * 获取相对时间描述
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @returns 相对时间描述
 *
 * @example
 * getRelativeTime(Date.now() - 60000) // '1分钟前'
 * getRelativeTime(Date.now() + 3600000) // '1小时后'
 */
export const getRelativeTime = (timestamp: number | string): string => {
  if (!timestamp) return "";

  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;
  if (isNaN(time) || time <= 0) return "";

  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const now = Date.now();
  const diff = now - milliseconds;
  const absDiff = Math.abs(diff);

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;
  const year = 365 * day;

  const suffix = diff > 0 ? "前" : "后";

  if (absDiff < minute) {
    return "刚刚";
  } else if (absDiff < hour) {
    return `${Math.floor(absDiff / minute)}分钟${suffix}`;
  } else if (absDiff < day) {
    return `${Math.floor(absDiff / hour)}小时${suffix}`;
  } else if (absDiff < month) {
    return `${Math.floor(absDiff / day)}天${suffix}`;
  } else if (absDiff < year) {
    return `${Math.floor(absDiff / month)}个月${suffix}`;
  } else {
    return `${Math.floor(absDiff / year)}年${suffix}`;
  }
};

/**
 * 判断是否为今天
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @returns 是否为今天
 */
export const isToday = (timestamp: number | string): boolean => {
  if (!timestamp) return false;

  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;
  if (isNaN(time) || time <= 0) return false;

  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const date = new Date(milliseconds);
  const today = new Date();

  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  );
};

/**
 * 判断是否为本周
 * @param timestamp 时间戳（支持10位秒级或13位毫秒级）
 * @returns 是否为本周
 */
export const isThisWeek = (timestamp: number | string): boolean => {
  if (!timestamp) return false;

  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;
  if (isNaN(time) || time <= 0) return false;

  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const date = new Date(milliseconds);
  const today = new Date();

  // 获取本周的开始时间（周一）
  const startOfWeek = new Date(today);
  const day = today.getDay();
  const diff = today.getDate() - day + (day === 0 ? -6 : 1);
  startOfWeek.setDate(diff);
  startOfWeek.setHours(0, 0, 0, 0);

  // 获取本周的结束时间（周日）
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);

  return date >= startOfWeek && date <= endOfWeek;
};
