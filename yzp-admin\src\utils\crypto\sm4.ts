import { sm4 } from "sm-crypto";

/**
 * SM4加密工具类
 * 用于API请求和响应的加密解密处理
 */
export class SM4Crypto {
  private static readonly DEFAULT_KEY = "1234567890abcdef1234567890abcdef"; // 32位默认密钥
  private key: string;

  constructor(key?: string) {
    this.key = key || SM4Crypto.DEFAULT_KEY;

    // 验证密钥长度
    if (this.key.length !== 32) {
      throw new Error("SM4密钥长度必须为32位");
    }
  }

  /**
   * 设置加密密钥
   * @param key 32位密钥
   */
  setKey(key: string): void {
    if (key.length !== 32) {
      throw new Error("SM4密钥长度必须为32位");
    }
    this.key = key;
  }

  /**
   * 加密数据
   * @param data 要加密的数据
   * @returns 加密后的十六进制字符串
   */
  encrypt(data: string): string {
    try {
      return sm4.encrypt(data, this.key);
    } catch (error) {
      console.error("SM4加密失败:", error);
      throw new Error("数据加密失败");
    }
  }

  /**
   * 解密数据
   * @param encryptedData 加密的十六进制字符串
   * @returns 解密后的原始数据
   */
  decrypt(encryptedData: string): string {
    try {
      return sm4.decrypt(encryptedData, this.key);
    } catch (error) {
      console.error("SM4解密失败:", error);
      throw new Error("数据解密失败");
    }
  }

  /**
   * 加密对象（将对象转为JSON字符串后加密）
   * @param obj 要加密的对象
   * @returns 加密后的十六进制字符串
   */
  encryptObject(obj: any): string {
    try {
      const jsonString = JSON.stringify(obj);
      return this.encrypt(jsonString);
    } catch (error) {
      console.error("对象加密失败:", error);
      throw new Error("对象加密失败");
    }
  }

  /**
   * 解密对象（解密后解析为JSON对象）
   * @param encryptedData 加密的十六进制字符串
   * @returns 解密后的对象
   */
  decryptObject<T = any>(encryptedData: string): T {
    try {
      const decryptedString = this.decrypt(encryptedData);
      return JSON.parse(decryptedString);
    } catch (error) {
      console.error("对象解密失败:", error);
      throw new Error("对象解密失败");
    }
  }

  /**
   * 判断数据是否需要加密
   * @param url 请求URL
   * @param method 请求方法
   * @returns 是否需要加密
   */
  static shouldEncrypt(url: string, method: string): boolean {
    // 加密白名单 - 这些接口不需要加密
    const encryptWhiteList = [
      "/login",
      "/refresh-token",
      "/generateCode",
      "/captcha"
    ];

    // 检查是否在白名单中
    const isInWhiteList = encryptWhiteList.some(whiteUrl =>
      url.includes(whiteUrl)
    );

    // 只对POST、PUT、PATCH请求进行加密，且不在白名单中
    const shouldEncryptMethod = ["POST", "PUT", "PATCH"].includes(
      method.toUpperCase()
    );

    return shouldEncryptMethod && !isInWhiteList;
  }
}

// 创建默认实例
export const sm4Crypto = new SM4Crypto();

// 设置全局密钥的方法
export const setSM4Key = (key: string): void => {
  sm4Crypto.setKey(key);
};
