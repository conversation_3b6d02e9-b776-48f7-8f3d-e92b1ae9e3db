// src/store/modules/encryptApi.ts
import { defineStore } from "pinia";
import { ref } from "vue";

export interface MsgListItem {
  uri: string;
  encryptType: number;
}

const DEFAULT_API_LIST: MsgListItem[] = [
  { uri: "/easyzhipin-admin/msg/list", encryptType: 1 }
];

export const useEncryptApiStore = defineStore(
  "encryptApi",
  () => {
    const apiList = ref<MsgListItem[]>([...DEFAULT_API_LIST]);

    const setApiList = (list?: MsgListItem[]) => {
      if (Array.isArray(list) && list.length > 0) {
        apiList.value = list;
      } else {
        apiList.value = [...DEFAULT_API_LIST];
      }
    };

    return {
      apiList,
      setApiList
    };
  },
  {
    persist: true
  }
);
