<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import { formatTimestamp } from "@/utils/dateFormat";
import { handleCertificateAudit } from "@/api/user_section/index";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const noteText = ref("");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: "note"
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {
  const res: any = await handleCertificateAudit(params);
  if (res.code === 0) {
    ElMessage.success("操作成功");
  } else {
    ElMessage.error(res.message);
  }
};

async function handlePass(value: any) {
  await handleAudit({
    id: infoTableData.value.id,
    status: value === "pass" ? 3 : 1
  });
  cancelBtn();
}
async function handleReject(value: any) {
  if (!noteText.value || noteText.value.trim() === "") {
    ElMessage.warning("请填写驳回原因");
    return;
  }
  await handleAudit({
    id: infoTableData.value.id,
    status: value === "reject" ? 2 : 1,
    reason: noteText.value
  });
  cancelBtn();
}
function handleTransfer() {
  cancelBtn();
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  rightType.value = "note"; // 或者根据业务回到初始
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

const noteOptions = [
  {
    label: "自定义批注",
    value: "none",
    content: ""
  },
  {
    label: "资料不合规",
    value: "lack",
    content: "您提交的资料不合规，请重新提交。"
  }
];

watch(
  () => $props.currentRow,
  newVal => {
    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status: newVal?.status === 3 ? "1" : newVal?.status === 2 ? "2" : "0",
      reason: newVal?.manualInspectionReason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.attachmentUrl;
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息卡片 -->
      <el-table
        :data="[infoTableData]"
        border
        class="info-table info-table-top"
        style="width: 100%; margin-bottom: 32px"
        :show-header="true"
        :header-cell-style="{
          background: '#fff',
          color: '#888',
          fontWeight: 500,
          fontSize: '16px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#fff',
          color: '#222',
          fontSize: '18px',
          fontWeight: 500,
          textAlign: 'center'
        }"
      >
        <el-table-column
          prop="createUserId"
          label="提交人ID"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="scope">
            {{ `ID：${scope.row.createUserId}` }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="提交人姓名" min-width="100" />
        <el-table-column prop="age" label="年龄" min-width="80" />
        <el-table-column prop="sex" label="性别" min-width="80">
          <template #default="scope">
            {{ scope.row.sex === 1 ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="certificate"
          label="证书名称"
          show-overflow-tooltip
          min-width="140"
        />
        <el-table-column prop="phone" label="提交人电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          <template #default="scope">
            {{ formatTimestamp(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧证书图片 -->
        <div class="card-box">
          <div class="card-title">证书图片</div>
          <el-image class="cert-img" :src="avatarUrl" fit="contain" />
        </div>
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        >
        <el-button type="danger" @click="handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="handlePass('pass')">通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
}
.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  margin-right: 24px;
}
.info-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.info-value {
  color: #222;
  font-size: 18px;
  font-weight: 500;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
</style>
