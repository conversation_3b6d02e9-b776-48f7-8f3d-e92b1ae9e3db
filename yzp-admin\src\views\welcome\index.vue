<script setup lang="ts">
import { ref } from "vue";

defineOptions({
  name: "Welcome"
});

const todayCount = ref(999);
const userCount = ref(25);
const companyCount = ref(25);
</script>

<template>
  <div class="dashboard-cards">
    <!-- 今日待审批 -->
    <div class="card card-today">
      <div class="card-header">
        <span class="card-tag">今</span>
        <span class="card-title">今日待审批</span>
      </div>
      <div class="card-progress">
        <svg width="80" height="80" viewBox="0 0 80 80">
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#fff"
            stroke-width="8"
            fill="none"
            opacity="0.3"
          />
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#fff"
            stroke-width="8"
            fill="none"
            stroke-linecap="round"
            :stroke-dasharray="201.06"
            :stroke-dashoffset="201.06 - (todayCount / 1000) * 201.06"
          />
        </svg>
        <span class="card-num">{{ todayCount }}</span>
      </div>
    </div>
    <!-- 用户审批 -->
    <div class="card card-user">
      <div class="card-header">
        <span class="card-avatar"></span>
        <span class="card-title">用户审批</span>
      </div>
      <div class="card-progress">
        <svg width="80" height="80" viewBox="0 0 80 80">
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#e6e6e6"
            stroke-width="8"
            fill="none"
          />
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#ffc107"
            stroke-width="8"
            fill="none"
            stroke-linecap="round"
            :stroke-dasharray="201.06"
            :stroke-dashoffset="201.06 - (userCount / 100) * 201.06"
          />
        </svg>
        <span class="card-num">{{ userCount }}</span>
      </div>
    </div>
    <!-- 公司审批 -->
    <div class="card card-company">
      <div class="card-header">
        <span class="card-avatar"></span>
        <span class="card-title">公司审批</span>
      </div>
      <div class="card-progress">
        <svg width="80" height="80" viewBox="0 0 80 80">
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#e6e6e6"
            stroke-width="8"
            fill="none"
          />
          <circle
            cx="40"
            cy="40"
            r="32"
            stroke="#4caf50"
            stroke-width="8"
            fill="none"
            stroke-linecap="round"
            :stroke-dasharray="201.06"
            :stroke-dashoffset="201.06 - (companyCount / 100) * 201.06"
          />
        </svg>
        <span class="card-num">{{ companyCount }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-cards {
  display: flex;
  gap: 24px;
  padding: 32px 0;
}
.card {
  width: 180px;
  min-height: 220px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px dashed #b3c0d1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18px 0 0 0;
  position: relative;
}
.card-today {
  background: #ffb3b3;
  border: none;
}
.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}
.card-tag {
  display: inline-block;
  background: #fff;
  color: #ff7b7b;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}
.card-title {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
.card-today .card-title {
  color: #fff;
}
.card-user .card-title,
.card-company .card-title {
  color: #333;
}
.card-avatar {
  width: 32px;
  height: 32px;
  background: #e6e6e6;
  border-radius: 50%;
  margin-bottom: 4px;
}
.card-progress {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.card-num {
  position: absolute;
  left: 0;
  right: 0;
  top: 28px;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}
.card-user .card-num,
.card-company .card-num {
  color: #333;
}
</style>
