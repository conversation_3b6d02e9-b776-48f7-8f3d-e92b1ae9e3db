<template>
  <div class="assign-detail-page">
    <!-- 顶部tab -->
    <div class="assign-tabs">
      <el-button
        v-for="tab in tabs"
        :key="tab"
        :type="activeTab === tab ? 'primary' : 'default'"
        class="assign-tab-btn"
        @click="activeTab = tab"
      >
        {{ tab }}
      </el-button>
    </div>

    <!-- 筛选区 -->
    <div class="assign-filter">
      <el-form :inline="true" class="assign-filter-form">
        <el-form-item label="状态" class="assign-form-item">
          <el-select v-model="status" style="width: 180px">
            <el-option label="未分配" value="未分配" />
            <el-option label="已分配" value="已分配" />
          </el-select>
        </el-form-item>
        <div class="assign-filter-actions">
          <el-button type="primary">搜索</el-button>
          <el-button>重置</el-button>
        </div>
      </el-form>
    </div>

    <!-- 分配申批 -->
    <div class="assign-content">
      <div class="assign-title-row">
        <div class="assign-title-left">
          <span class="assign-title">分配申批</span>
          <el-form :inline="true" class="assign-form-inline">
            <el-form-item label="审批人员">
              <el-input
                v-model="searchUser"
                placeholder="请输入审批人员名称"
                style="width: 220px"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="openAddUserDialog"
                >添加人员</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <span class="assign-count">
          待审批：<span class="assign-count-num">999</span>
        </span>
      </div>
      <el-table :data="userList" class="assign-table" border>
        <el-table-column label="#" width="50">
          <template #default="scope">
            <span :class="'assign-index assign-index-' + (scope.$index + 1)">
              {{ scope.$index + 1 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="80" />
        <el-table-column prop="status" label="状态" width="100" />
        <el-table-column label="明细">
          <template #default>
            <span class="assign-detail-item">简历--229</span>
            <span class="assign-detail-item">附件--229</span>
            <span class="assign-detail-item">企业入驻--229</span>
            <span class="assign-detail-item">岗位发布--229</span>
          </template>
        </el-table-column>
        <el-table-column label="分配" width="180">
          <template #default="scope">
            <el-input
              v-model="scope.row.assignNum"
              placeholder="输入分配数量"
            />
          </template>
        </el-table-column>
        <el-table-column width="120">
          <template #default>
            <el-button type="primary">确认分配</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加人员弹窗 -->
    <el-dialog v-model="addUserDialogVisible" title="添加人员" width="60%">
      <div class="add-user-filter-row">
        <el-form :inline="true">
          <el-form-item label="部门分组">
            <el-select v-model="group" style="width: 180px">
              <el-option label="A组" value="A组" />
              <el-option label="B组" value="B组" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchAccount"
              placeholder="搜索姓名/账号"
              style="width: 260px"
            />
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="candidateList"
        style="margin-top: 16px; margin-bottom: 50px"
      >
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="account" label="账号" />
        <el-table-column prop="group" label="分组" />
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleAddUserConfirm(scope.row)"
              >确认</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const tabs = [
  "头像",
  "简历",
  "附件",
  "作品集",
  "企业入驻",
  "企业信息",
  "地址",
  "岗位"
];
const activeTab = ref("头像");
const status = ref("未分配");
const searchUser = ref("");
const userList = ref([
  { name: "张三", status: "待审核", assignNum: "" },
  { name: "张三", status: "待审核", assignNum: "99999" },
  { name: "张三", status: "待审核", assignNum: "99999" }
]);

// 弹窗控制
const addUserDialogVisible = ref(false);

// 新增人员弹窗相关
const group = ref("A组");
const searchAccount = ref("");
const candidateList = ref([
  { name: "张三", account: "********", group: "A组" }
  // ...可扩展
]);
const selectedUser = ref<any>(null);

// 打开弹窗
function openAddUserDialog() {
  addUserDialogVisible.value = true;
  selectedUser.value = null;
}

// 确认添加
function handleAddUserConfirm(row) {
  userList.value.push({ ...row });
  addUserDialogVisible.value = false;
}
</script>

<style scoped lang="scss">
.assign-detail-page {
  background: #f5f7fa;
  padding: 32px;
  height: 85%;
}
.assign-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}
.assign-tab-btn {
  border-radius: 8px;
  font-size: 16px;
  padding: 8px 32px;
}
.assign-filter {
  background: #fff;
  border-radius: 10px;
  padding: 24px 32px;
  margin-bottom: 24px;
}
.assign-filter-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.assign-form-item {
  margin-bottom: 0;
}
.assign-filter-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}
.assign-content {
  background: #fff;
  border-radius: 10px;
  padding: 24px;
}
.assign-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 18px;
}
.assign-title-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.assign-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}
.assign-form-inline {
  display: flex;
  align-items: center;
  gap: 12px;
}
.assign-count {
  font-size: 16px;
  color: #666;
  margin-top: 8px;
}
.assign-count-num {
  color: #ff3c3c;
  font-weight: 700;
  margin-left: 4px;
}
.assign-detail-item {
  margin-right: 16px;
  color: #666;
}
.assign-index {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  line-height: 24px;
  font-weight: 700;
  color: #fff;
  background: #e6eaff;
}
.assign-index-1 {
  background: #ff4d4f;
}
.assign-index-2 {
  background: #1890ff;
}
.assign-index-3 {
  background: #52c41a;
}
.add-user-filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 12px;
}
</style>
