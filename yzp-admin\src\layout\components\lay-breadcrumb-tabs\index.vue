<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { isEqual } from "@pureadmin/utils";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
import Close from "~icons/ri/close-line";

const route = useRoute();
const router = useRouter();

// 获取多标签页数据
const multiTags = computed(() => {
  return useMultiTagsStoreHook().multiTags;
});

// 判断是否为首页标签
const isHomeTag = (item: any) => {
  return item.path === "/welcome" || item.meta?.title === "首页";
};

// 判断标签是否激活
const isActiveTag = (item: any) => {
  if (Object.keys(route.query).length > 0) {
    return isEqual(route.query, item?.query) && route.path === item.path;
  } else if (Object.keys(route.params).length > 0) {
    return isEqual(route.params, item?.params) && route.path === item.path;
  } else {
    return route.path === item.path;
  }
};

// 标签点击事件
const handleTagClick = (item: any) => {
  if (isActiveTag(item)) return;

  if (item.query) {
    router.push({
      path: item.path,
      query: item.query
    });
  } else if (item.params) {
    router.push({
      path: item.path,
      params: item.params
    });
  } else {
    router.push(item.path);
  }
};

// 关闭标签
const handleCloseTag = (item: any, event: Event) => {
  event.stopPropagation();

  // 不能关闭首页标签
  if (isHomeTag(item)) return;

  const index = multiTags.value.findIndex((tag: any) => {
    if (tag.query && item.query) {
      return isEqual(tag.query, item.query) && tag.path === item.path;
    } else if (tag.params && item.params) {
      return isEqual(tag.params, item.params) && tag.path === item.path;
    } else {
      return tag.path === item.path;
    }
  });

  if (index === -1) return;

  // 如果关闭的是当前激活的标签，需要跳转到其他标签
  if (isActiveTag(item)) {
    const nextTag = multiTags.value[index + 1] || multiTags.value[index - 1];
    if (nextTag) {
      handleTagClick(nextTag);
    }
  }

  // 从store中删除标签
  useMultiTagsStoreHook().handleTags("splice", "", {
    startIndex: index,
    length: 1
  });
};

onMounted(() => {
  // 组件挂载时的初始化逻辑
});
</script>

<template>
  <div class="breadcrumb-tabs">
    <div class="tabs-container">
      <div
        v-for="(item, index) in multiTags"
        :key="`${item.path}-${index}`"
        :class="[
          'tab-item',
          {
            'tab-item--active': isActiveTag(item),
            'tab-item--home': isHomeTag(item)
          }
        ]"
        @click="handleTagClick(item)"
      >
        <span class="tab-title">{{ item.meta?.title || item.name }}</span>
        <span
          v-if="!isHomeTag(item)"
          class="tab-close"
          @click="handleCloseTag(item, $event)"
        >
          <IconifyIconOffline :icon="Close" />
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.breadcrumb-tabs {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 8px 16px;
  border-top: 1px solid #f0f1f4;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .tabs-container {
    display: flex;
    align-items: center;
    gap: 8px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .tab-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 12px;
    color: #666;
    transition: all 0.2s;
    min-height: 24px;

    &:hover {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &--active {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }

    &--home {
      background: #f6ffed;
      border-color: #52c41a;
      color: #52c41a;
      font-weight: 500;

      &:hover {
        background: #f6ffed;
        border-color: #52c41a;
        color: #52c41a;
      }

      &.tab-item--active {
        background: #f6ffed;
        border-color: #52c41a;
        color: #52c41a;
      }
    }
  }

  .tab-title {
    margin-right: 4px;
    line-height: 1;
  }

  .tab-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    transition: all 0.2s;
    margin-left: 2px;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    svg {
      width: 8px;
      height: 8px;
    }
  }
}

/* 暗色主题适配 */
html.dark .breadcrumb-tabs {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);

  .tab-item {
    background: var(--el-fill-color-light);
    border-color: var(--el-border-color);
    color: var(--el-text-color-regular);

    &:hover {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-7);
      color: var(--el-color-primary);
    }

    &--active {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }

    &--home {
      background: rgba(103, 194, 58, 0.1);
      border-color: #67c23a;
      color: #67c23a;

      &:hover,
      &.tab-item--active {
        background: rgba(103, 194, 58, 0.2);
        border-color: #67c23a;
        color: #67c23a;
      }
    }
  }

  .tab-close {
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
