import {
	env_dev_sm4,
	env_pro_sm4
} from "@/request/env.js"
var sm4 = env_dev_sm4
// sm4 加密
export function encryption(params) {
	const SM4 = require("sm-crypto").sm4
	const key = sm4; // 提供的密钥
	return SM4.encrypt(params, key);
}

// sm4 解密
export function decryption(params) {
	const SM4 = require("sm-crypto").sm4
	const key = sm4; // 提供的密钥
	return SM4.decrypt(params, key); // 第一个参数是加密数据的编码，第二个参数是输出编码
}