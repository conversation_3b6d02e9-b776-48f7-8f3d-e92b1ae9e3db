<script setup lang="ts">
import { initRouter } from "@/router/utils";
import { ReDialog } from "@/components/ReDialog";
import {onMounted, onUnmounted, PropType, ref, watch} from "vue";
import {TippyOptions} from "vue-tippy";
import {menuType} from "@/layout/types";
const props = defineProps({
  showDialog: {
    type: Boolean,
    default: false
  },
  data: {
    type: String,
    default: ""
  }
});
watch(() => props.data, (newVal) => {
  console.log(`Initial message is: ${newVal}`);
}, { immediate: true });
watch(() => props.showDialog, (newVal) => {
  if(newVal){
    console.log('======================new',newVal)
    init(newVal);
  }
}, { immediate: true });
onUnmounted(() => {

});
const isDialogVisible = ref(true);
function init(form) {
  isDialogVisible.value = true;
}
</script>

<template>
  <div>
    <ReDialog v-model="isDialogVisible" title="标题">
      <div>
        <div>
          审核图片
        </div>
        <div>
          <div></div>
          <div></div>
        </div>
      </div>
    </ReDialog>
  </div>
</template>
<style scoped lang="scss">

</style>
