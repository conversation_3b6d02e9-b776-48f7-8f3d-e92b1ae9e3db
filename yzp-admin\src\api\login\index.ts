import { http } from "@/utils/http";

// 获取图形验证码
export const getGenerateCode = () => {
  return http.request(
    "get",
    "/easyzhipin-admin/login/generateCode",
    {},
    {
      responseType: "arraybuffer"
    }
  );
};

// 登录接口
export const login = (data: any) => {
  return http.request("post", "/easyzhipin-admin/login/login", { data });
};

// 登出接口
export const loginOut = () => {
  return http.request("post", "/easyzhipin-admin/login/logout");
};
