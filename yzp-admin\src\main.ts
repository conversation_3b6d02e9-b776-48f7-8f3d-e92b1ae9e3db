import { createApp, type Directive } from "vue";
import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import { getPlatformConfig } from "./config";
import { MotionPlugin } from "@vueuse/motion";
import { useElementPlus } from "@/plugins/elementPlus";
import { injectResponsiveStorage } from "@/utils/responsive";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

import Table from "@pureadmin/table";
// import PureDescriptions from "@pureadmin/descriptions";

// 引入样式
import "./style/reset.scss";
import "./style/index.scss";
import "./style/tailwind.css";
import "element-plus/dist/index.css";
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";

// 创建 app 实例
const app = createApp(App);

// 注册 Pinia（提前注册）
const pinia = createPinia();
app.use(pinia);
pinia.use(piniaPluginPersistedstate);

// 安装 store（必须在 app.use(pinia) 之后）
setupStore(app);

// 自定义指令
import * as directives from "@/directives";
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 全局注册图标组件
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);

// 注册按钮权限组件
import { Auth } from "@/components/ReAuth";
import { Perms } from "@/components/RePerms";
app.component("Auth", Auth);
app.component("Perms", Perms);

// 注册 vue-tippy
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import VueTippy from "vue-tippy";
app.use(VueTippy);

// 配置平台初始化逻辑，注册其余插件
getPlatformConfig(app).then(async config => {
  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(useElementPlus).use(Table);
  // .use(PureDescriptions)
  // .use(useEcharts); // 如果需要 echarts 插件

  app.mount("#app");
});

// 处理加密接口
import { useEncryptApiStore } from "@/store/modules/encryptApi";
import { getMsgList } from "@/api/msg/index"; // 你自己的接口方法

const encryptApiStore = useEncryptApiStore();

// 项目启动时调用
(async () => {
  try {
    const res = await getMsgList();
    const encryptList = res.data.filter(item => item.encryptType === 1);
    encryptApiStore.setApiList(encryptList);
  } catch (err) {
    console.log("🚀 ~ getMsgList ~ err:", err);
  }
})();
