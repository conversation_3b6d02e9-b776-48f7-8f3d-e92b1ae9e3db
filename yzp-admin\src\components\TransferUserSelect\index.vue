<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
  transferList: Array<{ name: string; value: string }>;
  modelValue: string;
}>();
const emit = defineEmits(["update:modelValue", "submit"]);

const transferValue = ref(props.modelValue);

// 双向绑定
watch(
  () => props.modelValue,
  val => (transferValue.value = val)
);
watch(
  () => transferValue.value,
  val => emit("update:modelValue", val)
);

function handleSubmit() {
  emit("submit", transferValue.value);
}
</script>

<template>
  <div class="transfer-select-box">
    <div class="record-title">转审人员</div>
    <div class="scroll-container">
      <el-table
        :data="transferList"
        border
        class="transfer-table"
        :show-header="true"
        :header-cell-style="{
          background: '#f7f8fa',
          color: '#888',
          fontWeight: 500,
          fontSize: '15px',
          textAlign: 'left'
        }"
        :cell-style="{
          background: '#f7f8fa',
          color: '#222',
          fontSize: '15px',
          textAlign: 'left'
        }"
      >
        <el-table-column prop="name" label="名字" min-width="180">
          <template #default="scope">
            <el-radio
              v-model="transferValue"
              :label="scope.row.value"
              style="margin-left: 8px"
            >
              {{ scope.row.name }}
            </el-radio>
          </template>
        </el-table-column>
      </el-table>
      <div class="transfer-btn-bar">
        <el-button type="primary" style="width: 100px" @click="handleSubmit"
          >转审</el-button
        >
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.transfer-select-box {
  background: #fff;
  border-radius: 12px;
  padding: 24px 24px 18px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 380px;
  max-height: 400px; // 整体最大高度（可选）
  overflow: hidden;
}

.scroll-container {
  max-height: 240px; // 设置滚动区域最大高度
  overflow-y: auto;
}

.record-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}

.transfer-table {
  background: #f7f8fa;
  border-radius: 8px;
  overflow: hidden;
}

.transfer-btn-bar {
  text-align: right;
  margin-top: 10px;
}
</style>
