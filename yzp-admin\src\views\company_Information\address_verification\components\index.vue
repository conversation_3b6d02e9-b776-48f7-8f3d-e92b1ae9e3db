<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { formatTimestamp } from "@/utils/dateFormat";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import {
  getCompanyImageById,
  handleCompanyAddressAudit
} from "@/api/company/index";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");
const companyImage = ref<any>([]);

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 获取公司图片
const getCompanyImage = async () => {
  const res: any = await getCompanyImageById({ id: infoTableData.value.id });
  if (res.code === 0) {
    companyImage.value = res.data.slice(0, 2);
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {
  const res: any = await handleCompanyAddressAudit(params);
  if (res.code === 0) {
    ElMessage.success("操作成功");
    cancelBtn();
  }
};

// 通过
function handlePass(value: string) {
  const params = {
    id: infoTableData.value.id,
    auditStatus: value === "pass" ? 1 : 0
  };
  handleAudit(params);
  $emit("update:updataList", true);
}

// 驳回
function handleReject(value: string) {
  if (!noteText.value || noteText.value.trim() === "") {
    ElMessage.warning("请填写驳回原因");
    return;
  }
  const params = {
    id: infoTableData.value.id,
    auditStatus: value === "reject" ? 2 : 0,
    reason: noteText.value
  };
  handleAudit(params);
}

function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  cancelBtn();
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

const noteOptions = [
  {
    label: "自定义批注",
    value: "none",
    content: ""
  },
  {
    label: "资料不合规",
    value: "lack",
    content: "您提交的资料不合规，请重新提交。"
  }
];
const noteText = ref("");

watch(
  () => $props.currentRow,
  newVal => {
    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status: newVal?.status === 1 ? "1" : newVal?.status === 2 ? "2" : "0",
      reason: newVal?.reason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
    getCompanyImage();
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息区：两个el-table分两行，表头隐藏，内容上下分布 -->
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%; margin-bottom: 12px"
        class="info-table-normal"
      >
        <el-table-column
          prop="name"
          show-overflow-tooltip
          label="企业名称"
          min-width="180"
        />
        <el-table-column
          prop="socialCreditCode"
          show-overflow-tooltip
          label="社会信用代码"
          min-width="180"
        />
        <el-table-column
          prop="enterpriseLegalPerson"
          label="法人"
          min-width="120"
        />
      </el-table>
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%"
        class="info-table-normal"
      >
        <el-table-column prop="id" label="提交人ID" min-width="100">
          <template #default="scope"> {{ `ID：${scope.row.id}` }} </template>
        </el-table-column>
        <el-table-column
          prop="createUserName"
          label="提交人姓名"
          min-width="120"
        />
        <el-table-column prop="phone" label="提交人电话" min-width="80" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          <template #default="scope">
            {{ formatTimestamp(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧公司照片 -->
        <div class="img-card-row">
          <template v-if="companyImage && companyImage.length > 0">
            <div
              v-for="(item, index) in companyImage"
              :key="index"
              class="img-card"
            >
              <div class="img-title">公司照片</div>
              <el-image
                v-if="item.fileIdUrl"
                :src="item.fileIdUrl"
                class="img-preview"
                fit="contain"
                :preview-src-list="[item.fileIdUrl]"
                :initial-index="0"
              >
                <template #error>
                  <div class="img-placeholder" />
                </template>
              </el-image>
              <div v-else class="img-placeholder" />
            </div>
          </template>
          <!-- 暂无数据展示 -->
          <div v-else class="no-data-card">
            <div class="no-data-text">暂无数据</div>
          </div>
          <!-- <div class="img-card">
            <div class="img-title">公司照片</div>
            <el-image
              v-if="infoTableData.employmentCert"
              :src="infoTableData.employmentCert"
              class="img-preview"
              fit="contain"
              :preview-src-list="[infoTableData.employmentCert]"
              :initial-index="0"
            >
              <template #error>
                <div class="img-placeholder" />
              </template>
            </el-image>
            <div v-else class="img-placeholder" />
          </div> -->
        </div>
        <!-- 右侧内容保持不变 -->
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        >
        <el-button type="danger" @click="() => handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="() => handlePass('pass')"
          >通过</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
